<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZZShop本地商业平台 - 技术人员招聘需求</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 40px;
        }
        .job-card {
            background: #f8f9fa;
            border-left: 5px solid #3498db;
            padding: 25px;
            margin: 30px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .job-title {
            color: #2c3e50;
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .job-icon {
            width: 40px;
            height: 40px;
            background: #3498db;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .section {
            margin: 20px 0;
        }
        .section-title {
            color: #34495e;
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 5px;
        }
        .requirements-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 15px 0;
        }
        .requirement-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        .requirement-item h4 {
            color: #e74c3c;
            margin: 0 0 10px 0;
            font-size: 1.1em;
        }
        .skill-tag {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 4px 12px;
            margin: 3px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        .skill-tag.advanced {
            background: #e74c3c;
        }
        .skill-tag.preferred {
            background: #f39c12;
        }
        .salary-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .benefits {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        ul {
            margin: 10px 0;
            padding-left: 25px;
        }
        li {
            margin: 8px 0;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .tech-stack {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ZZShop本地商业平台 - 技术人员招聘需求</h1>
        
        <!-- 后端开发工程师 -->
        <div class="job-card">
            <div class="job-title">
                <div class="job-icon">BE</div>
                高级后端开发工程师 (Python/FastAPI)
            </div>
            
            <div class="salary-info">
                <h3 style="margin: 0;">薪资待遇：面议 · 13薪</h3>
                <p style="margin: 10px 0 0 0;">工作地点：本地 | 工作经验：3-8年 | 学历要求：本科及以上</p>
            </div>

            <div class="section">
                <div class="section-title">🎯 岗位职责</div>
                <ul>
                    <li>负责ZZShop平台后端微服务架构设计与开发</li>
                    <li>基于FastAPI框架开发高性能RESTful API和gRPC服务</li>
                    <li>设计和优化PostgreSQL数据库架构，实现读写分离和分库分表</li>
                    <li>开发支付、订单、商户、用户等核心业务模块</li>
                    <li>集成AI能力：智能推荐、风控模型、价格优化、客服机器人等</li>
                    <li>对接政务、税务、银行等外部系统API</li>
                    <li>负责系统性能优化，确保10万+并发用户稳定运行</li>
                    <li>参与技术方案评审，指导初级开发人员</li>
                </ul>
            </div>

            <div class="section">
                <div class="section-title">💻 核心技术要求</div>
                <div class="tech-stack">
                    <h4>必备技能 (Must Have):</h4>
                    <span class="skill-tag advanced">Python 3.9+</span>
                    <span class="skill-tag advanced">FastAPI</span>
                    <span class="skill-tag advanced">SQLAlchemy ORM</span>
                    <span class="skill-tag advanced">PostgreSQL</span>
                    <span class="skill-tag advanced">Redis</span>
                    <span class="skill-tag advanced">Docker</span>
                    <span class="skill-tag advanced">RESTful API</span>
                    <span class="skill-tag advanced">AI集成</span>
                    <span class="skill-tag advanced">Git</span>
                </div>
                
                <div class="requirements-grid">
                    <div class="requirement-item">
                        <h4>框架与语言</h4>
                        <ul>
                            <li>精通Python 3.9+，熟悉异步编程(AsyncIO)</li>
                            <li>深度掌握FastAPI框架，了解Pydantic数据验证</li>
                            <li>熟悉SQLAlchemy ORM，能够设计复杂数据模型</li>
                            <li>了解gRPC协议和Protocol Buffers</li>
                        </ul>
                    </div>
                    <div class="requirement-item">
                        <h4>数据库与缓存</h4>
                        <ul>
                            <li>精通PostgreSQL，熟悉索引优化、查询调优</li>
                            <li>掌握Redis集群，熟悉缓存策略和分布式锁</li>
                            <li>了解MongoDB文档数据库</li>
                            <li>具备数据库分库分表经验</li>
                        </ul>
                    </div>
                    <div class="requirement-item">
                        <h4>微服务与中间件</h4>
                        <ul>
                            <li>熟悉微服务架构设计模式</li>
                            <li>掌握Kafka/RabbitMQ消息队列</li>
                            <li>了解服务注册发现、配置中心</li>
                            <li>熟悉API网关(Kong)使用</li>
                        </ul>
                    </div>
                    <div class="requirement-item">
                        <h4>DevOps与部署</h4>
                        <ul>
                            <li>熟练使用Docker容器化技术</li>
                            <li>了解Kubernetes基本概念</li>
                            <li>掌握CI/CD流程，熟悉GitLab</li>
                            <li>具备Linux服务器运维经验</li>
                        </ul>
                    </div>
                    <div class="requirement-item">
                        <h4>AI与机器学习</h4>
                        <ul>
                            <li>熟悉AI API集成(OpenAI、百度、阿里云等)</li>
                            <li>了解推荐算法和协同过滤</li>
                            <li>掌握基础机器学习概念</li>
                            <li>能够集成智能客服、文本分析等AI服务</li>
                            <li>具备AI编程思维，熟练使用AI编程助手</li>
                            <li>熟练利用AI编辑器提升开发效率</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">⭐ 加分项技能</div>
                <div class="tech-stack">
                    <span class="skill-tag preferred">Kubernetes</span>
                    <span class="skill-tag preferred">Elasticsearch</span>
                    <span class="skill-tag preferred">Prometheus</span>
                    <span class="skill-tag preferred">Grafana</span>
                    <span class="skill-tag preferred">Jaeger</span>
                    <span class="skill-tag preferred">支付系统</span>
                    <span class="skill-tag preferred">金融风控</span>
                    <span class="skill-tag preferred">高并发优化</span>
                    <span class="skill-tag preferred">TensorFlow</span>
                    <span class="skill-tag preferred">PyTorch</span>
                    <span class="skill-tag preferred">LangChain</span>
                    <span class="skill-tag preferred">向量数据库</span>
                </div>
                <ul>
                    <li>有支付系统、电商平台开发经验</li>
                    <li>熟悉金融风控、反欺诈系统设计</li>
                    <li>具备高并发、高可用系统设计经验</li>
                    <li>了解分布式事务处理(Saga模式)</li>
                    <li>熟悉监控系统(Prometheus + Grafana)</li>
                    <li>有政务系统对接经验</li>
                    <li>具备AI模型部署和推理服务经验</li>
                    <li>熟悉大语言模型(LLM)集成和应用</li>
                    <li>了解RAG(检索增强生成)架构</li>
                    <li>有推荐系统、个性化算法开发经验</li>
                </ul>
            </div>

            <div class="section">
                <div class="section-title">📋 任职要求</div>
                <ul>
                    <li>计算机相关专业本科及以上学历</li>
                    <li>3年以上Python后端开发经验，有大型项目经验</li>
                    <li>具备良好的代码规范和文档编写习惯</li>
                    <li>有团队协作精神，能够承受一定工作压力</li>
                    <li>学习能力强，对新技术有敏锐度</li>
                    <li>有电商、O2O、支付相关项目经验优先</li>
                </ul>
            </div>

            <div class="benefits">
                <div class="section-title">🎁 福利待遇</div>
                <ul>
                    <li>五险一金 + 补充商业保险</li>
                    <li>年终奖 + 项目奖金 + 股权激励</li>
                    <li>弹性工作时间，每周双休</li>
                    <li>技术培训津贴，参加技术大会</li>
                    <li>团建活动，年度旅游</li>
                    <li>优质办公环境，双显示器配置</li>
                </ul>
            </div>
        </div>

        <!-- 前端开发工程师 -->
        <div class="job-card">
            <div class="job-title">
                <div class="job-icon">FE</div>
                高级前端开发工程师 (React/Flutter)
            </div>
            
            <div class="salary-info">
                <h3 style="margin: 0;">薪资待遇：面议 · 13薪</h3>
                <p style="margin: 10px 0 0 0;">工作地点：本地 | 工作经验：3-6年 | 学历要求：本科及以上</p>
            </div>

            <div class="section">
                <div class="section-title">🎯 岗位职责</div>
                <ul>
                    <li>负责ZZShop平台多端前端应用开发(Web + 移动端)</li>
                    <li>基于React + TypeScript开发用户端、商户端、管理后台</li>
                    <li>使用Flutter开发跨平台移动应用(iOS/Android)</li>
                    <li>开发微信小程序，实现快速购物体验</li>
                    <li>集成AI功能：智能客服界面、语音识别、图像识别、个性化推荐展示</li>
                    <li>与UI/UX设计师协作，实现高质量用户界面</li>
                    <li>优化前端性能，确保良好的用户体验</li>
                    <li>参与前端架构设计，制定开发规范</li>
                </ul>
            </div>

            <div class="section">
                <div class="section-title">💻 核心技术要求</div>
                <div class="tech-stack">
                    <h4>必备技能 (Must Have):</h4>
                    <span class="skill-tag advanced">React 18</span>
                    <span class="skill-tag advanced">TypeScript</span>
                    <span class="skill-tag advanced">Flutter</span>
                    <span class="skill-tag advanced">Dart</span>
                    <span class="skill-tag advanced">微信小程序</span>
                    <span class="skill-tag advanced">Ant Design</span>
                    <span class="skill-tag advanced">Vite</span>
                    <span class="skill-tag advanced">AI界面集成</span>
                    <span class="skill-tag advanced">Git</span>
                </div>
                
                <div class="requirements-grid">
                    <div class="requirement-item">
                        <h4>Web前端技术</h4>
                        <ul>
                            <li>精通React 18 + TypeScript，熟悉Hooks</li>
                            <li>掌握Ant Design Pro组件库</li>
                            <li>熟悉Vite构建工具，了解Webpack</li>
                            <li>掌握状态管理(Redux/Zustand)</li>
                            <li>了解PWA技术，Service Worker</li>
                        </ul>
                    </div>
                    <div class="requirement-item">
                        <h4>移动端开发</h4>
                        <ul>
                            <li>精通Flutter 3.16 + Dart 3.2</li>
                            <li>熟悉Flutter状态管理(Provider/Riverpod)</li>
                            <li>掌握原生插件开发和平台通道</li>
                            <li>了解iOS/Android平台特性</li>
                            <li>具备应用发布和版本管理经验</li>
                        </ul>
                    </div>
                    <div class="requirement-item">
                        <h4>小程序开发</h4>
                        <ul>
                            <li>熟练开发微信小程序(WXML/WXSS/WXS)</li>
                            <li>掌握小程序生命周期和组件化</li>
                            <li>熟悉微信支付、授权等API</li>
                            <li>了解小程序性能优化技巧</li>
                            <li>具备小程序发布和审核经验</li>
                        </ul>
                    </div>
                    <div class="requirement-item">
                        <h4>工程化与工具</h4>
                        <ul>
                            <li>熟悉前端工程化，模块化开发</li>
                            <li>掌握ESLint、Prettier代码规范</li>
                            <li>了解前端测试(Jest/Cypress)</li>
                            <li>熟悉Git版本控制和团队协作</li>
                        </ul>
                    </div>
                    <div class="requirement-item">
                        <h4>AI前端集成</h4>
                        <ul>
                            <li>熟悉AI API调用和数据处理</li>
                            <li>掌握语音识别、图像上传等交互</li>
                            <li>了解聊天机器人界面开发</li>
                            <li>能够实现智能推荐列表展示</li>
                            <li>具备AI编程思维，熟练使用AI编程助手</li>
                            <li>熟练利用AI编辑器提升开发效率</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">⭐ 加分项技能</div>
                <div class="tech-stack">
                    <span class="skill-tag preferred">Vue.js</span>
                    <span class="skill-tag preferred">React Native</span>
                    <span class="skill-tag preferred">Next.js</span>
                    <span class="skill-tag preferred">Electron</span>
                    <span class="skill-tag preferred">WebGL</span>
                    <span class="skill-tag preferred">Canvas</span>
                    <span class="skill-tag preferred">数据可视化</span>
                    <span class="skill-tag preferred">性能优化</span>
                    <span class="skill-tag preferred">WebRTC</span>
                    <span class="skill-tag preferred">WebAssembly</span>
                    <span class="skill-tag preferred">AI模型部署</span>
                    <span class="skill-tag preferred">TensorFlow.js</span>
                </div>
                <ul>
                    <li>有电商、O2O平台前端开发经验</li>
                    <li>熟悉数据可视化(ECharts/D3.js)</li>
                    <li>了解WebGL、Canvas高级图形技术</li>
                    <li>有React Native开发经验</li>
                    <li>熟悉前端性能监控和优化</li>
                    <li>了解Node.js后端开发</li>
                    <li>有设计能力，能够独立完成UI设计</li>
                    <li>熟悉TensorFlow.js或ONNX.js前端AI模型部署</li>
                    <li>有语音识别、图像处理前端集成经验</li>
                    <li>了解WebRTC实时音视频通信</li>
                    <li>具备智能客服、聊天机器人界面开发经验</li>
                </ul>
            </div>

            <div class="section">
                <div class="section-title">📋 任职要求</div>
                <ul>
                    <li>计算机相关专业本科及以上学历</li>
                    <li>3年以上前端开发经验，有多端开发经验</li>
                    <li>具备良好的代码规范和组件设计能力</li>
                    <li>有良好的审美能力和用户体验意识</li>
                    <li>学习能力强，关注前端技术发展趋势</li>
                    <li>有移动端应用开发和发布经验优先</li>
                </ul>
            </div>

            <div class="benefits">
                <div class="section-title">🎁 福利待遇</div>
                <ul>
                    <li>五险一金 + 补充商业保险</li>
                    <li>年终奖 + 项目奖金 + 股权激励</li>
                    <li>弹性工作时间，每周双休</li>
                    <li>技术培训津贴，参加技术大会</li>
                    <li>团建活动，年度旅游</li>
                    <li>MacBook Pro + 双显示器工作环境</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <div class="section-title">📞 联系方式</div>
            <p><strong>投递邮箱：</strong> <EMAIL></p>
            <p><strong>联系电话：</strong> 400-XXX-XXXX</p>
            <p><strong>公司地址：</strong> [具体地址]</p>
            <p><strong>投递要求：</strong> 请在邮件标题注明"应聘职位-姓名-工作年限"，并附上个人简历和作品集</p>
        </div>
    </div>
</body>
</html>
