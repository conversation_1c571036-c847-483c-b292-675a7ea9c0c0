# 本地商业平台设计文档

## 概述

本设计文档基于需求文档，为本地商业平台提供详细的技术架构设计。平台采用微服务架构，支持四个主要端：销售平台（用户端）、管理平台（运营端）、商户端和骑手端，能够承载 10 万并发用户的访问量。

## 架构设计

### 整体架构

```mermaid
graph TB
    subgraph "客户端层"
        A[销售平台App]
        B[销售平台小程序]
        C[销售平台Web]
        D[商户端App]
        E[商户端Web]
        F[骑手端App]
        G[管理平台Web]
    end

    subgraph "网关层"
        H[API网关]
        I[负载均衡器]
    end

    subgraph "服务层"
        J[用户服务]
        K[商户服务]
        L[商品服务]
        M[订单服务]
        N[支付服务]
        O[配送服务]
        P[活动服务]
        Q[通知服务]
    end

    subgraph "数据层"
        R[PostgreSQL集群]
        S[Redis集群]
        T[MongoDB]
        U[Elasticsearch]
    end

    subgraph "基础设施"
        V[消息队列]
        W[文件存储]
        X[监控系统]
        Y[日志系统]
    end

    A --> H
    B --> H
    C --> H
    D --> H
    E --> H
    F --> H
    G --> H

    H --> I
    I --> J
    I --> K
    I --> L
    I --> M
    I --> N
    I --> O
    I --> P
    I --> Q

    J --> R
    K --> R
    L --> R
    M --> R
    N --> R
    O --> R
    P --> R
    Q --> S

    L --> U
    J --> S
    M --> T

    J --> V
    M --> V
    O --> V
    Q --> V

    J --> W
    L --> W
```

### 技术栈选择

#### 后端技术栈

- **开发语言**: Python 3.11 + FastAPI
- **Web 框架**: FastAPI + Uvicorn
- **ORM**: SQLAlchemy 2.0 + Alembic
- **数据库**: PostgreSQL 15（主数据库）+ Redis 7.x（缓存）+ MongoDB（日志存储）
- **消息队列**: Apache Kafka + aiokafka
- **API 网关**: Kong Gateway
- **服务注册与发现**: Consul
- **配置管理**: Consul KV
- **异步处理**: Celery + Redis
- **容器化**: Docker + Kubernetes
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

#### 前端技术栈

- **移动端跨平台**:
  - Flutter 3.x（iOS + Android 统一开发）
  - 华为鸿蒙: ArkTS + ArkUI（单独开发）
- **小程序**: 微信小程序原生开发
- **Web 端**: Vue.js 3 + TypeScript + Element Plus
- **状态管理**:
  - Flutter: Provider + Riverpod
  - Web: Pinia
- **HTTP 客户端**:
  - Flutter: Dio
  - Web: Axios

## 组件和接口设计

### 核心服务组件

#### 1. 用户服务 (User Service)

**职责**: 用户注册、登录、个人信息管理、权限控制

**核心接口**:

```python
from fastapi import APIRouter, Depends, HTTPException
from typing import List

router = APIRouter(prefix="/api/v1/users", tags=["users"])

@router.post("/register", response_model=UserResponse)
async def register(request: RegisterRequest):
    """用户注册"""
    pass

@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """用户登录"""
    pass

@router.get("/{user_id}", response_model=UserProfile)
async def get_user_profile(user_id: int, current_user: User = Depends(get_current_user)):
    """获取用户资料"""
    pass

@router.put("/{user_id}", response_model=UserProfile)
async def update_user_profile(user_id: int, request: UpdateUserRequest, current_user: User = Depends(get_current_user)):
    """更新用户资料"""
    pass

@router.get("/{user_id}/coupons", response_model=List[CouponResponse])
async def get_user_coupons(user_id: int, current_user: User = Depends(get_current_user)):
    """获取用户优惠券"""
    pass
```

#### 2. 商户服务 (Merchant Service)

**职责**: 商户入驻、审核、店铺管理、商户信息维护

**核心接口**:

```python
from fastapi import APIRouter, Depends
from typing import List

router = APIRouter(prefix="/api/v1/merchants", tags=["merchants"])

@router.post("/apply", response_model=ApplicationResponse)
async def apply_for_merchant(request: MerchantApplicationRequest):
    """商户入驻申请"""
    pass

@router.get("/{merchant_id}", response_model=MerchantProfile)
async def get_merchant_profile(merchant_id: int):
    """获取商户资料"""
    pass

@router.put("/{merchant_id}", response_model=MerchantProfile)
async def update_merchant_profile(merchant_id: int, request: UpdateMerchantRequest, current_user: User = Depends(get_current_merchant)):
    """更新商户资料"""
    pass

@router.get("/{merchant_id}/statistics", response_model=MerchantStatistics)
async def get_merchant_statistics(merchant_id: int, current_user: User = Depends(get_current_merchant)):
    """获取商户统计数据"""
    pass
```

#### 3. 商品服务 (Product Service)

**职责**: 商品管理、分类管理、库存管理、搜索功能

**核心接口**:

```python
from fastapi import APIRouter, Depends, Query
from typing import List, Optional

router = APIRouter(prefix="/api/v1/products", tags=["products"])

@router.post("/", response_model=ProductResponse)
async def create_product(request: CreateProductRequest, current_user: User = Depends(get_current_merchant)):
    """创建商品"""
    pass

@router.get("/{product_id}", response_model=ProductDetail)
async def get_product_detail(product_id: int):
    """获取商品详情"""
    pass

@router.get("/search", response_model=PageResponse[ProductSummary])
async def search_products(
    keyword: Optional[str] = Query(None),
    category_id: Optional[int] = Query(None),
    min_price: Optional[float] = Query(None),
    max_price: Optional[float] = Query(None),
    city_code: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100)
):
    """搜索商品"""
    pass

@router.get("/categories", response_model=List[CategoryResponse])
async def get_categories():
    """获取商品分类"""
    pass

@router.put("/{product_id}/inventory")
async def update_inventory(product_id: int, request: UpdateInventoryRequest, current_user: User = Depends(get_current_merchant)):
    """更新商品库存"""
    pass
```

#### 4. 订单服务 (Order Service)

**职责**: 订单创建、状态管理、订单查询、退款处理

**核心接口**:

```python
from fastapi import APIRouter, Depends, Query

router = APIRouter(prefix="/api/v1/orders", tags=["orders"])

@router.post("/", response_model=OrderResponse)
async def create_order(request: CreateOrderRequest, current_user: User = Depends(get_current_user)):
    """创建订单"""
    pass

@router.get("/{order_id}", response_model=OrderDetail)
async def get_order_detail(order_id: int, current_user: User = Depends(get_current_user)):
    """获取订单详情"""
    pass

@router.get("/user/{user_id}", response_model=PageResponse[OrderSummary])
async def get_user_orders(
    user_id: int,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user)
):
    """获取用户订单列表"""
    pass

@router.put("/{order_id}/status")
async def update_order_status(order_id: int, request: UpdateOrderStatusRequest, current_user: User = Depends(get_current_merchant)):
    """更新订单状态"""
    pass

@router.post("/{order_id}/refund", response_model=RefundResponse)
async def refund_order(order_id: int, request: RefundRequest, current_user: User = Depends(get_current_user)):
    """申请退款"""
    pass
```

#### 5. 支付服务 (Payment Service)

**职责**: 支付处理、补贴计算、财务管理

**核心接口**:

```python
from fastapi import APIRouter, Depends

router = APIRouter(prefix="/api/v1/payments", tags=["payments"])

@router.post("/create", response_model=PaymentResponse)
async def create_payment(request: CreatePaymentRequest, current_user: User = Depends(get_current_user)):
    """创建支付订单"""
    pass

@router.post("/callback")
async def payment_callback(request: PaymentCallbackRequest):
    """支付回调接口"""
    pass

@router.get("/{payment_id}", response_model=PaymentDetail)
async def get_payment_detail(payment_id: int, current_user: User = Depends(get_current_user)):
    """获取支付详情"""
    pass

@router.post("/subsidies/calculate", response_model=SubsidyCalculationResponse)
async def calculate_subsidy(request: SubsidyCalculationRequest, current_user: User = Depends(get_current_user)):
    """计算补贴金额"""
    pass
```

#### 6. 配送服务 (Delivery Service)

**职责**: 配送订单管理、骑手调度、配送状态跟踪

**核心接口**:

```python
from fastapi import APIRouter, Depends
from typing import List

router = APIRouter(prefix="/api/v1/delivery", tags=["delivery"])

@router.post("/orders", response_model=DeliveryOrderResponse)
async def create_delivery_order(request: CreateDeliveryOrderRequest):
    """创建配送订单"""
    pass

@router.get("/orders/{delivery_order_id}", response_model=DeliveryOrderDetail)
async def get_delivery_order_detail(delivery_order_id: int):
    """获取配送订单详情"""
    pass

@router.put("/orders/{delivery_order_id}/assign")
async def assign_delivery_order(delivery_order_id: int, request: AssignDeliveryRequest):
    """分配配送订单"""
    pass

@router.get("/riders/{rider_id}/orders", response_model=List[DeliveryOrderSummary])
async def get_rider_orders(rider_id: int, current_user: User = Depends(get_current_rider)):
    """获取骑手订单列表"""
    pass

@router.put("/orders/{delivery_order_id}/status")
async def update_delivery_status(delivery_order_id: int, request: UpdateDeliveryStatusRequest, current_user: User = Depends(get_current_rider)):
    """更新配送状态"""
    pass
```

#### 7. 活动服务 (Activity Service)

**职责**: 营销活动管理、优惠券发放、活动效果统计

**核心接口**:

```python
from fastapi import APIRouter, Depends
from typing import List

router = APIRouter(prefix="/api/v1/activities", tags=["activities"])

@router.post("/", response_model=ActivityResponse)
async def create_activity(request: CreateActivityRequest, current_user: User = Depends(get_current_admin)):
    """创建活动"""
    pass

@router.get("/{activity_id}", response_model=ActivityDetail)
async def get_activity_detail(activity_id: int):
    """获取活动详情"""
    pass

@router.get("/active", response_model=List[ActivitySummary])
async def get_active_activities():
    """获取进行中的活动"""
    pass

@router.post("/{activity_id}/participate", response_model=ParticipationResponse)
async def participate_activity(activity_id: int, request: ParticipationRequest, current_user: User = Depends(get_current_user)):
    """参与活动"""
    pass

@router.get("/{activity_id}/statistics", response_model=ActivityStatistics)
async def get_activity_statistics(activity_id: int, current_user: User = Depends(get_current_admin)):
    """获取活动统计"""
    pass
```

### API 网关设计

API 网关作为所有客户端请求的统一入口，提供以下功能：

1. **路由管理**: 根据请求路径将请求转发到相应的微服务
2. **认证授权**: JWT token 验证和权限控制
3. **限流熔断**: 防止系统过载
4. **监控日志**: 记录 API 调用情况
5. **跨域处理**: 支持 Web 端跨域请求

```yaml
# Kong API网关配置示例
services:
  - name: user-service
    url: http://user-service:8000
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 1000
      - name: jwt
        config:
          secret_is_base64: false
    routes:
      - name: user-routes
        paths:
          - /api/v1/users

  - name: product-service
    url: http://product-service:8000
    plugins:
      - name: rate-limiting
        config:
          minute: 200
          hour: 2000
      - name: circuit-breaker
        config:
          failure_threshold: 5
          recovery_timeout: 30
    routes:
      - name: product-routes
        paths:
          - /api/v1/products
```

## 数据模型设计

### 核心数据模型

#### 用户模型 (User)

```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    email VARCHAR(100),
    password_hash VARCHAR(255) NOT NULL,
    nickname VARCHAR(50),
    avatar_url VARCHAR(255),
    gender SMALLINT DEFAULT 0, -- 0:未知,1:男,2:女
    birth_date DATE,
    city_code VARCHAR(10),
    address TEXT,
    status SMALLINT DEFAULT 1, -- 1:正常,2:禁用
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_city_code ON users(city_code);
CREATE INDEX idx_users_status ON users(status);

-- 自动更新updated_at的触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### 商户模型 (Merchant)

```sql
CREATE TABLE merchants (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    business_name VARCHAR(100) NOT NULL,
    business_license VARCHAR(50) UNIQUE NOT NULL,
    contact_person VARCHAR(50) NOT NULL,
    contact_phone VARCHAR(20) NOT NULL,
    business_address TEXT NOT NULL,
    longitude DECIMAL(10,7),
    latitude DECIMAL(10,7),
    business_hours VARCHAR(100),
    description TEXT,
    logo_url VARCHAR(255),
    banner_urls JSONB, -- PostgreSQL的JSONB类型性能更好
    category_id INTEGER,
    status SMALLINT DEFAULT 0, -- 0:待审核,1:正常,2:禁用
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_sales BIGINT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_merchants_user_id FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE INDEX idx_merchants_status ON merchants(status);
CREATE INDEX idx_merchants_category ON merchants(category_id);
CREATE INDEX idx_merchants_location ON merchants USING GIST(point(longitude, latitude));
CREATE INDEX idx_merchants_banner_urls ON merchants USING GIN(banner_urls);

CREATE TRIGGER update_merchants_updated_at BEFORE UPDATE ON merchants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### 商品模型 (Product)

```sql
CREATE TABLE products (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category_id INTEGER NOT NULL,
    brand VARCHAR(100),
    sku VARCHAR(100),
    price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2),
    stock_quantity INTEGER DEFAULT 0,
    min_order_quantity INTEGER DEFAULT 1,
    max_order_quantity INTEGER DEFAULT 999,
    images JSONB,
    specifications JSONB,
    weight DECIMAL(8,3),
    status SMALLINT DEFAULT 0, -- 0:待审核,1:上架,2:下架,3:缺货
    sales_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    review_count INTEGER DEFAULT 0,
    search_vector tsvector, -- PostgreSQL全文搜索
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_products_merchant_id FOREIGN KEY (merchant_id) REFERENCES merchants(id)
);

CREATE INDEX idx_products_merchant ON products(merchant_id);
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_products_search ON products USING GIN(search_vector);
CREATE INDEX idx_products_images ON products USING GIN(images);
CREATE INDEX idx_products_specifications ON products USING GIN(specifications);

-- 全文搜索向量更新触发器
CREATE OR REPLACE FUNCTION update_product_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector = to_tsvector('chinese', COALESCE(NEW.name, '') || ' ' || COALESCE(NEW.description, ''));
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_products_search_vector BEFORE INSERT OR UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_product_search_vector();
```

#### 订单模型 (Order)

```sql
CREATE TABLE orders (
    id BIGSERIAL PRIMARY KEY,
    order_no VARCHAR(32) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    merchant_id BIGINT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    subsidy_amount DECIMAL(10,2) DEFAULT 0.00,
    final_amount DECIMAL(10,2) NOT NULL,
    payment_method SMALLINT, -- 1:微信,2:支付宝,3:银行卡
    delivery_type SMALLINT, -- 1:自提,2:配送,3:快递
    delivery_address JSONB,
    delivery_fee DECIMAL(8,2) DEFAULT 0.00,
    status SMALLINT DEFAULT 1, -- 1:待支付,2:待发货,3:待收货,4:已完成,5:已取消,6:退款中,7:已退款
    remark TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_orders_user_id FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_orders_merchant_id FOREIGN KEY (merchant_id) REFERENCES merchants(id)
);

CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_merchant ON orders(merchant_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_order_no ON orders(order_no);
CREATE INDEX idx_orders_delivery_address ON orders USING GIN(delivery_address);

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### PostgreSQL特有优势

1. **JSONB数据类型**: 高效存储和查询JSON数据，适合商品规格、订单详情等复杂数据
2. **全文搜索**: 内置中文全文搜索，无需额外的Elasticsearch（可选）
3. **地理位置查询**: PostGIS扩展支持高效的地理位置查询，适合商家位置搜索
4. **并发性能**: MVCC机制提供更好的并发读写性能
5. **数组类型**: 原生支持数组，适合存储商品图片列表等
6. **窗口函数**: 强大的分析查询能力，适合复杂的统计报表

### 数据库分片策略

考虑到 10 万并发用户的需求，采用以下分片策略：

1. **用户数据分片**: 按用户 ID 进行水平分片，每个分片存储 100 万用户数据
2. **订单数据分片**: 按订单创建时间进行分片，每月一个分片
3. **商品数据**: 按商户 ID 进行分片，同一商户的商品存储在同一分片
4. **读写分离**: 主库负责写操作，从库负责读操作，读写比例约为 7:3

### PostgreSQL性能优化配置

```sql
-- 性能优化配置示例
-- postgresql.conf 关键配置
shared_buffers = '256MB'                    -- 共享缓冲区
effective_cache_size = '1GB'                -- 有效缓存大小
work_mem = '4MB'                           -- 工作内存
maintenance_work_mem = '64MB'              -- 维护工作内存
checkpoint_completion_target = 0.9         -- 检查点完成目标
wal_buffers = '16MB'                       -- WAL缓冲区
default_statistics_target = 100            -- 统计信息目标
random_page_cost = 1.1                     -- 随机页面成本（SSD优化）

-- 连接池配置
max_connections = 200                       -- 最大连接数
```

## 错误处理设计

### 统一错误响应格式

```python
from pydantic import BaseModel
from typing import Optional, Generic, TypeVar
import time

T = TypeVar('T')

class ApiResponse(BaseModel, Generic[T]):
    code: int
    message: str
    data: Optional[T] = None
    timestamp: int

    @classmethod
    def success(cls, data: T = None, message: str = "success") -> "ApiResponse[T]":
        return cls(
            code=200,
            message=message,
            data=data,
            timestamp=int(time.time() * 1000)
        )

    @classmethod
    def error(cls, code: int, message: str) -> "ApiResponse[None]":
        return cls(
            code=code,
            message=message,
            timestamp=int(time.time() * 1000)
        )
```

### 错误码定义

```python
from enum import Enum

class ErrorCode(Enum):
    # 系统错误码
    SUCCESS = (200, "操作成功")
    BAD_REQUEST = (400, "请求参数错误")
    UNAUTHORIZED = (401, "未授权访问")
    FORBIDDEN = (403, "禁止访问")
    NOT_FOUND = (404, "资源不存在")
    INTERNAL_ERROR = (500, "系统内部错误")

    # 业务错误码
    USER_NOT_FOUND = (1001, "用户不存在")
    USER_ALREADY_EXISTS = (1002, "用户已存在")
    INVALID_PASSWORD = (1003, "密码错误")
    MERCHANT_NOT_APPROVED = (2001, "商户未通过审核")
    PRODUCT_OUT_OF_STOCK = (3001, "商品库存不足")
    ORDER_NOT_FOUND = (4001, "订单不存在")
    PAYMENT_FAILED = (5001, "支付失败")

    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message
```

### 全局异常处理

```python
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import logging

logger = logging.getLogger(__name__)

class BusinessException(Exception):
    def __init__(self, error_code: ErrorCode):
        self.error_code = error_code
        super().__init__(error_code.message)

async def business_exception_handler(request: Request, exc: BusinessException):
    return JSONResponse(
        status_code=200,
        content=ApiResponse.error(exc.error_code.code, exc.error_code.message).dict()
    )

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=400,
        content=ApiResponse.error(400, "请求参数错误").dict()
    )

async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content=ApiResponse.error(exc.status_code, exc.detail).dict()
    )

async def generic_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unexpected error occurred: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content=ApiResponse.error(500, "系统内部错误").dict()
    )

def setup_exception_handlers(app: FastAPI):
    app.add_exception_handler(BusinessException, business_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(Exception, generic_exception_handler)
```

## 测试策略

### 测试金字塔

1. **单元测试 (70%)**

   - 使用 pytest + pytest-asyncio
   - 使用 pytest-mock 进行模拟测试
   - 覆盖所有业务逻辑和工具类
   - 目标代码覆盖率: 80%以上

2. **集成测试 (20%)**

   - 使用 TestContainers 进行数据库集成测试
   - API 接口测试使用 httpx + pytest
   - 消息队列集成测试使用 pytest-kafka

3. **端到端测试 (10%)**
   - 使用 Selenium 进行 Web 端自动化测试
   - 使用 Flutter 集成测试进行移动端测试
   - 关键业务流程测试

### 性能测试

1. **压力测试**: 使用 JMeter 模拟 10 万并发用户访问
2. **负载测试**: 测试系统在正常负载下的性能表现
3. **容量测试**: 确定系统的最大处理能力
4. **稳定性测试**: 长时间运行测试系统稳定性

### 测试环境

```yaml
# 测试环境配置
environments:
  dev:
    database: test_db_dev
    redis: redis_dev
    kafka: kafka_dev

  staging:
    database: test_db_staging
    redis: redis_staging
    kafka: kafka_staging

  production:
    database: prod_db
    redis: redis_prod
    kafka: kafka_prod
```

## 安全设计

### 认证授权

1. **JWT Token 认证**

   - Access Token 有效期: 2 小时
   - Refresh Token 有效期: 7 天
   - Token 自动刷新机制

2. **角色权限控制**
   - 用户角色: 普通用户、商户、骑手、管理员
   - 权限粒度: 接口级别权限控制
   - 使用 FastAPI Security + JWT

### 数据安全

1. **敏感数据加密**

   - 用户密码使用 BCrypt 加密
   - 手机号、身份证号等敏感信息使用 AES 加密
   - 数据库连接使用 SSL

2. **API 安全**

   - 接口限流: 防止恶意攻击
   - 参数校验: 防止 SQL 注入和 XSS 攻击
   - HTTPS 强制: 所有 API 使用 HTTPS

3. **数据脱敏**
   - 日志中敏感信息脱敏
   - 测试环境数据脱敏
   - 第三方接口数据脱敏

### 安全配置示例

```python
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from datetime import datetime, timedelta
import bcrypt

# JWT配置
SECRET_KEY = "your-secret-key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 120

security = HTTPBearer()

def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: int = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        return user_id
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

def get_current_user(user_id: int = Depends(verify_token)):
    # 从数据库获取用户信息
    user = get_user_by_id(user_id)
    if not user:
        raise HTTPException(status_code=401, detail="User not found")
    return user

def get_current_admin(current_user: User = Depends(get_current_user)):
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    return current_user

def get_current_merchant(current_user: User = Depends(get_current_user)):
    if current_user.role not in ["merchant", "admin"]:
        raise HTTPException(status_code=403, detail="Merchant access required")
    return current_user

def hash_password(password: str) -> str:
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
```

## 监控和运维

### 监控指标

1. **系统指标**

   - CPU 使用率、内存使用率、磁盘 IO
   - 网络流量、连接数
   - Python 进程内存、垃圾回收情况

2. **业务指标**

   - API 响应时间、QPS
   - 订单量、支付成功率
   - 用户活跃度、商户活跃度

3. **错误监控**
   - 异常日志统计
   - 接口错误率
   - 系统可用性

### 日志管理

```yaml
# Python日志配置
logging:
  version: 1
  disable_existing_loggers: false
  formatters:
    default:
      format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
    detailed:
      format: "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s"
  handlers:
    console:
      class: logging.StreamHandler
      level: INFO
      formatter: default
    file:
      class: logging.handlers.RotatingFileHandler
      level: INFO
      formatter: detailed
      filename: logs/application.log
      maxBytes: 104857600  # 100MB
      backupCount: 30
  loggers:
    platform:
      level: INFO
      handlers: [console, file]
    uvicorn:
      level: INFO
      handlers: [console, file]
```

### 部署架构

```yaml
# Kubernetes部署配置示例
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
        - name: user-service
          image: platform/user-service:latest
          ports:
            - containerPort: 8000
          env:
            - name: ENVIRONMENT
              value: "production"
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: db-secret
                  key: url
            - name: REDIS_URL
              valueFrom:
                secretKeyRef:
                  name: redis-secret
                  key: url
          resources:
            requests:
              memory: "512Mi"
              cpu: "500m"
            limits:
              memory: "1Gi"
              cpu: "1000m"
          livenessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 5
            periodSeconds: 5
```

## Flutter 跨平台架构设计

### Flutter 应用架构

采用 Clean Architecture + BLoC 模式，确保代码的可维护性和可测试性：

```
lib/
├── core/                 # 核心功能
│   ├── constants/       # 常量定义
│   ├── errors/          # 错误处理
│   ├── network/         # 网络请求
│   ├── utils/           # 工具类
│   └── widgets/         # 通用组件
├── features/            # 功能模块
│   ├── auth/           # 认证模块
│   │   ├── data/       # 数据层
│   │   ├── domain/     # 业务逻辑层
│   │   └── presentation/ # 表现层
│   ├── products/       # 商品模块
│   ├── orders/         # 订单模块
│   └── profile/        # 用户资料模块
└── main.dart           # 应用入口
```

### Flutter 状态管理

```dart
// 使用Riverpod进行状态管理
final userProvider = StateNotifierProvider<UserNotifier, UserState>((ref) {
  return UserNotifier(ref.read(userRepositoryProvider));
});

class UserNotifier extends StateNotifier<UserState> {
  final UserRepository _userRepository;

  UserNotifier(this._userRepository) : super(UserState.initial());

  Future<void> login(String username, String password) async {
    state = state.copyWith(isLoading: true);
    try {
      final user = await _userRepository.login(username, password);
      state = state.copyWith(user: user, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }
}
```

### Flutter 网络层设计

```dart
// 使用Dio进行网络请求
class ApiClient {
  late final Dio _dio;

  ApiClient() {
    _dio = Dio(BaseOptions(
      baseUrl: 'https://api.platform.com',
      connectTimeout: Duration(seconds: 5),
      receiveTimeout: Duration(seconds: 3),
    ));

    _dio.interceptors.addAll([
      AuthInterceptor(),
      LogInterceptor(),
      ErrorInterceptor(),
    ]);
  }

  Future<ApiResponse<T>> get<T>(String path, {Map<String, dynamic>? queryParameters}) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      return ApiResponse<T>.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }
}
```

### 华为鸿蒙系统单独开发

由于华为鸿蒙系统的特殊性，需要单独开发：

```typescript
// ArkTS + ArkUI开发示例
@Entry
@Component
struct ProductListPage {
  @State products: Product[] = []
  @State loading: boolean = false

  async aboutToAppear() {
    await this.loadProducts()
  }

  async loadProducts() {
    this.loading = true
    try {
      const response = await httpRequest.get('/api/v1/products/search')
      this.products = response.data
    } catch (error) {
      promptAction.showToast({ message: '加载失败' })
    } finally {
      this.loading = false
    }
  }

  build() {
    Column() {
      if (this.loading) {
        LoadingComponent()
      } else {
        List() {
          ForEach(this.products, (product: Product) => {
            ProductItem({ product: product })
          })
        }
      }
    }
    .width('100%')
    .height('100%')
  }
}
```

## 性能优化策略

### 后端性能优化

1. **数据库优化**
   - 使用连接池管理数据库连接
   - 实施读写分离和分库分表
   - 合理使用索引和查询优化

```python
# SQLAlchemy异步配置 - PostgreSQL
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

engine = create_async_engine(
    "postgresql+asyncpg://user:password@localhost/db",
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    echo=False
)

AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)
```

2. **缓存策略**
   - Redis 缓存热点数据
   - 应用层缓存减少数据库访问

```python
import redis.asyncio as redis
from functools import wraps

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expire_time: int = 300):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            cached_result = await redis_client.get(cache_key)

            if cached_result:
                return json.loads(cached_result)

            result = await func(*args, **kwargs)
            await redis_client.setex(cache_key, expire_time, json.dumps(result))
            return result
        return wrapper
    return decorator
```

### 前端性能优化

1. **Flutter 性能优化**
   - 使用 ListView.builder 进行大列表优化
   - 图片缓存和懒加载
   - 合理使用 const 构造函数

```dart
// 大列表优化示例
class ProductList extends StatelessWidget {
  final List<Product> products;

  const ProductList({Key? key, required this.products}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: products.length,
      itemBuilder: (context, index) {
        return ProductItem(product: products[index]);
      },
      cacheExtent: 1000, // 预缓存范围
    );
  }
}
```

2. **图片优化**

```dart
// 使用cached_network_image进行图片缓存
CachedNetworkImage(
  imageUrl: product.imageUrl,
  placeholder: (context, url) => const CircularProgressIndicator(),
  errorWidget: (context, url, error) => const Icon(Icons.error),
  memCacheWidth: 300, // 限制内存中图片大小
  memCacheHeight: 300,
)
```

这个设计文档提供了完整的技术架构、数据模型、接口设计、安全策略和运维方案，采用 Python FastAPI 后端和 Flutter 跨平台前端（华为鸿蒙单独开发），确保系统能够满足 10 万并发用户的性能要求，同时保证系统的可扩展性、安全性和可维护性。
