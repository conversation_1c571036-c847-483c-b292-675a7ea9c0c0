<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZZShop本地商业平台技术架构图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 10px;
            padding: 0;
            background-color: #f5f5f5;
            overflow-x: auto;
        }
        .architecture-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            min-width: 1400px;
            margin: 0 auto;
            max-width: 100%;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 40px;
            font-size: 2.2em;
            font-weight: bold;
        }
        .svg-container {
            width: 100%;
            overflow-x: auto;
            text-align: center;
        }
        svg {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="architecture-container">
        <h1>ZZShop本地商业平台技术架构图</h1>
        
        <div class="svg-container">
        <svg width="2200" height="1800" viewBox="0 0 2200 1800" xmlns="http://www.w3.org/2000/svg">
            <!-- 定义样式和渐变 -->
            <defs>
                <linearGradient id="clientGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="gatewayGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="serviceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#2ecc71;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#27ae60;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="dataGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#f39c12;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#e67e22;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="externalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#9b59b6;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#8e44ad;stop-opacity:1" />
                </linearGradient>
                <!-- 箭头标记 -->
                <marker id="arrowhead" markerWidth="20" markerHeight="14" refX="18" refY="7" orient="auto">
                    <polygon points="0 0, 20 7, 0 14" fill="#34495e"/>
                </marker>
            </defs>

            <!-- 背景层 -->
            <rect x="50" y="80" width="1500" height="280" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="3" rx="15"/>
            <text x="800" y="60" text-anchor="middle" fill="#2c3e50" font-size="32" font-weight="bold">客户端层 (Multi-Platform Client)</text>
            
            <rect x="50" y="400" width="1500" height="180" fill="#fadbd8" stroke="#e74c3c" stroke-width="3" rx="15"/>
            <text x="800" y="380" text-anchor="middle" fill="#2c3e50" font-size="32" font-weight="bold">网关与负载均衡层 (Gateway & Load Balancer)</text>
            
            <rect x="50" y="620" width="1500" height="460" fill="#d5f4e6" stroke="#27ae60" stroke-width="3" rx="15"/>
            <text x="800" y="600" text-anchor="middle" fill="#2c3e50" font-size="32" font-weight="bold">微服务业务层 (Business Microservices)</text>
            
            <rect x="50" y="1120" width="1500" height="280" fill="#fdeaa7" stroke="#f39c12" stroke-width="3" rx="15"/>
            <text x="800" y="1100" text-anchor="middle" fill="#2c3e50" font-size="32" font-weight="bold">数据存储层 (Data Storage Layer)</text>
            
            <rect x="50" y="1440" width="1500" height="160" fill="#e8daef" stroke="#9b59b6" stroke-width="3" rx="15"/>
            <text x="800" y="1420" text-anchor="middle" fill="#2c3e50" font-size="32" font-weight="bold">外部系统集成层 (External Systems Integration)</text>

            <!-- 客户端层组件 -->
            <!-- 用户端 -->
            <rect x="80" y="120" width="200" height="100" fill="url(#clientGradient)" rx="12"/>
            <text x="180" y="155" text-anchor="middle" fill="white" font-size="20" font-weight="bold">用户App</text>
            <text x="180" y="180" text-anchor="middle" fill="white" font-size="14">Flutter/React Native</text>
            <text x="180" y="200" text-anchor="middle" fill="white" font-size="12">浏览、下单、支付</text>
            
            <rect x="300" y="120" width="200" height="100" fill="url(#clientGradient)" rx="12"/>
            <text x="400" y="155" text-anchor="middle" fill="white" font-size="20" font-weight="bold">微信小程序</text>
            <text x="400" y="180" text-anchor="middle" fill="white" font-size="14">WeChat MiniProgram</text>
            <text x="400" y="200" text-anchor="middle" fill="white" font-size="12">快速下单、支付</text>
            
            <rect x="520" y="120" width="200" height="100" fill="url(#clientGradient)" rx="12"/>
            <text x="620" y="155" text-anchor="middle" fill="white" font-size="20" font-weight="bold">Web端</text>
            <text x="620" y="180" text-anchor="middle" fill="white" font-size="14">React/Vue SPA</text>
            <text x="620" y="200" text-anchor="middle" fill="white" font-size="12">PC端购物体验</text>

            <!-- 商户端 -->
            <rect x="760" y="120" width="200" height="100" fill="url(#clientGradient)" rx="12"/>
            <text x="860" y="155" text-anchor="middle" fill="white" font-size="20" font-weight="bold">商户App</text>
            <text x="860" y="180" text-anchor="middle" fill="white" font-size="14">Native/Hybrid</text>
            <text x="860" y="200" text-anchor="middle" fill="white" font-size="12">店铺管理、订单</text>
            
            <rect x="980" y="120" width="200" height="100" fill="url(#clientGradient)" rx="12"/>
            <text x="1080" y="155" text-anchor="middle" fill="white" font-size="20" font-weight="bold">商户Web</text>
            <text x="1080" y="180" text-anchor="middle" fill="white" font-size="14">Admin Dashboard</text>
            <text x="1080" y="200" text-anchor="middle" fill="white" font-size="12">数据统计、营销</text>

            <!-- 骑手端 -->
            <rect x="1200" y="120" width="200" height="100" fill="url(#clientGradient)" rx="12"/>
            <text x="1300" y="155" text-anchor="middle" fill="white" font-size="20" font-weight="bold">骑手App</text>
            <text x="1300" y="180" text-anchor="middle" fill="white" font-size="14">Delivery App</text>
            <text x="1300" y="200" text-anchor="middle" fill="white" font-size="12">实时配送、GPS</text>

            <!-- 管理端 -->
            <rect x="1420" y="120" width="200" height="100" fill="url(#clientGradient)" rx="12"/>
            <text x="1520" y="155" text-anchor="middle" fill="white" font-size="20" font-weight="bold">管理平台</text>
            <text x="1520" y="180" text-anchor="middle" fill="white" font-size="14">Admin Console</text>
            <text x="1520" y="200" text-anchor="middle" fill="white" font-size="12">运营管理、监控</text>

            <!-- 技术详情 -->
            <text x="180" y="250" text-anchor="middle" fill="#34495e" font-size="14" font-weight="bold">Flutter (Dart)</text>
            <text x="180" y="270" text-anchor="middle" fill="#7f8c8d" font-size="12">JWT认证、推送</text>
            <text x="180" y="290" text-anchor="middle" fill="#7f8c8d" font-size="12">离线缓存、同步</text>
            
            <text x="400" y="250" text-anchor="middle" fill="#34495e" font-size="14" font-weight="bold">WXML/WXS/WXSS</text>
            <text x="400" y="270" text-anchor="middle" fill="#7f8c8d" font-size="12">微信支付、授权</text>
            <text x="400" y="290" text-anchor="middle" fill="#7f8c8d" font-size="12">实时位置、分享</text>
            
            <text x="620" y="250" text-anchor="middle" fill="#34495e" font-size="14" font-weight="bold">React + TypeScript</text>
            <text x="620" y="270" text-anchor="middle" fill="#7f8c8d" font-size="12">响应式设计、PWA</text>
            <text x="620" y="290" text-anchor="middle" fill="#7f8c8d" font-size="12">Service Worker</text>
            
            <text x="860" y="250" text-anchor="middle" fill="#34495e" font-size="14" font-weight="bold">Flutter (Dart)</text>
            <text x="860" y="270" text-anchor="middle" fill="#7f8c8d" font-size="12">店铺管理</text>
            <text x="860" y="290" text-anchor="middle" fill="#7f8c8d" font-size="12">实时通知</text>
            
            <text x="1080" y="250" text-anchor="middle" fill="#34495e" font-size="14" font-weight="bold">React + TypeScript</text>
            <text x="1080" y="270" text-anchor="middle" fill="#7f8c8d" font-size="12">数据可视化</text>
            <text x="1080" y="290" text-anchor="middle" fill="#7f8c8d" font-size="12">Ant Design Pro</text>
            
            <text x="1300" y="250" text-anchor="middle" fill="#34495e" font-size="14" font-weight="bold">Flutter + GPS SDK</text>
            <text x="1300" y="270" text-anchor="middle" fill="#7f8c8d" font-size="12">实时定位</text>
            <text x="1300" y="290" text-anchor="middle" fill="#7f8c8d" font-size="12">路线优化</text>
            
            <text x="1520" y="250" text-anchor="middle" fill="#34495e" font-size="14" font-weight="bold">React + TypeScript</text>
            <text x="1520" y="270" text-anchor="middle" fill="#7f8c8d" font-size="12">数据分析</text>
            <text x="1520" y="290" text-anchor="middle" fill="#7f8c8d" font-size="12">系统监控</text>

            <!-- 网关层 -->
            <rect x="80" y="440" width="280" height="120" fill="url(#gatewayGradient)" rx="12"/>
            <text x="220" y="480" text-anchor="middle" fill="white" font-size="22" font-weight="bold">API网关 (Kong)</text>
            <text x="220" y="505" text-anchor="middle" fill="white" font-size="14">路由、限流、鉴权</text>
            <text x="220" y="525" text-anchor="middle" fill="white" font-size="12">OAuth2.0 + JWT</text>
            <text x="220" y="540" text-anchor="middle" fill="white" font-size="12">API版本管理</text>
            
            <rect x="380" y="440" width="280" height="120" fill="url(#gatewayGradient)" rx="12"/>
            <text x="520" y="480" text-anchor="middle" fill="white" font-size="22" font-weight="bold">负载均衡 (Nginx)</text>
            <text x="520" y="505" text-anchor="middle" fill="white" font-size="14">反向代理、SSL终结</text>
            <text x="520" y="525" text-anchor="middle" fill="white" font-size="12">健康检查、灵活路由</text>
            <text x="520" y="540" text-anchor="middle" fill="white" font-size="12">缩放支持</text>
            
            <rect x="680" y="440" width="280" height="120" fill="url(#gatewayGradient)" rx="12"/>
            <text x="820" y="480" text-anchor="middle" fill="white" font-size="22" font-weight="bold">CDN加速</text>
            <text x="820" y="505" text-anchor="middle" fill="white" font-size="14">静态资源缓存</text>
            <text x="820" y="525" text-anchor="middle" fill="white" font-size="12">图片、JS、CSS</text>
            <text x="820" y="540" text-anchor="middle" fill="white" font-size="12">全球加速节点</text>
            
            <rect x="980" y="440" width="280" height="120" fill="url(#gatewayGradient)" rx="12"/>
            <text x="1120" y="480" text-anchor="middle" fill="white" font-size="22" font-weight="bold">安全防护</text>
            <text x="1120" y="505" text-anchor="middle" fill="white" font-size="14">WAF、DDoS防护</text>
            <text x="1120" y="525" text-anchor="middle" fill="white" font-size="12">防火墙、入侵检测</text>
            <text x="1120" y="540" text-anchor="middle" fill="white" font-size="12">数据加密传输</text>

            <!-- 微服务层 -->
            <!-- 第一行服务 -->
            <rect x="80" y="660" width="220" height="140" fill="url(#serviceGradient)" rx="12"/>
            <text x="190" y="710" text-anchor="middle" fill="white" font-size="20" font-weight="bold">用户服务</text>
            <text x="190" y="735" text-anchor="middle" fill="white" font-size="14">User Authentication</text>
            <text x="190" y="755" text-anchor="middle" fill="white" font-size="12">登录注册、权限管理</text>
            <text x="190" y="775" text-anchor="middle" fill="white" font-size="12">OAuth2、多端同步</text>
            
            <rect x="320" y="660" width="220" height="140" fill="url(#serviceGradient)" rx="12"/>
            <text x="430" y="710" text-anchor="middle" fill="white" font-size="20" font-weight="bold">商户服务</text>
            <text x="430" y="735" text-anchor="middle" fill="white" font-size="14">Merchant Management</text>
            <text x="430" y="755" text-anchor="middle" fill="white" font-size="12">店铺管理、认证</text>
            <text x="430" y="775" text-anchor="middle" fill="white" font-size="12">财务对接、结算</text>
            
            <rect x="560" y="660" width="220" height="140" fill="url(#serviceGradient)" rx="12"/>
            <text x="670" y="710" text-anchor="middle" fill="white" font-size="20" font-weight="bold">商品服务</text>
            <text x="670" y="735" text-anchor="middle" fill="white" font-size="14">Product Catalog</text>
            <text x="670" y="755" text-anchor="middle" fill="white" font-size="12">商品管理、库存</text>
            <text x="670" y="775" text-anchor="middle" fill="white" font-size="12">分类、搜索、推荐</text>
            
            <rect x="800" y="660" width="220" height="140" fill="url(#serviceGradient)" rx="12"/>
            <text x="910" y="710" text-anchor="middle" fill="white" font-size="20" font-weight="bold">订单服务</text>
            <text x="910" y="735" text-anchor="middle" fill="white" font-size="14">Order Management</text>
            <text x="910" y="755" text-anchor="middle" fill="white" font-size="12">订单创建、状态管理</text>
            <text x="910" y="775" text-anchor="middle" fill="white" font-size="12">工作流、自动化</text>

            <!-- 第二行服务 -->
            <rect x="1040" y="660" width="220" height="140" fill="url(#serviceGradient)" rx="12"/>
            <text x="1150" y="710" text-anchor="middle" fill="white" font-size="20" font-weight="bold">支付服务</text>
            <text x="1150" y="735" text-anchor="middle" fill="white" font-size="14">Payment Gateway</text>
            <text x="1150" y="755" text-anchor="middle" fill="white" font-size="12">多渠道支付、风控</text>
            <text x="1150" y="775" text-anchor="middle" fill="white" font-size="12">分账、对账系统</text>
            
            <rect x="1280" y="660" width="220" height="140" fill="url(#serviceGradient)" rx="12"/>
            <text x="1390" y="710" text-anchor="middle" fill="white" font-size="20" font-weight="bold">配送服务</text>
            <text x="1390" y="735" text-anchor="middle" fill="white" font-size="14">Delivery System</text>
            <text x="1390" y="755" text-anchor="middle" fill="white" font-size="12">智能调度、路线优化</text>
            <text x="1390" y="775" text-anchor="middle" fill="white" font-size="12">实时跟踪、估时</text>

            <!-- 第三行服务 -->
            <rect x="80" y="820" width="180" height="120" fill="url(#serviceGradient)" rx="12"/>
            <text x="170" y="865" text-anchor="middle" fill="white" font-size="18" font-weight="bold">活动服务</text>
            <text x="170" y="885" text-anchor="middle" fill="white" font-size="13">Campaign</text>
            <text x="170" y="905" text-anchor="middle" fill="white" font-size="11">优惠券、秒杀</text>
            <text x="170" y="920" text-anchor="middle" fill="white" font-size="11">会员等级</text>
            
            <rect x="280" y="820" width="180" height="120" fill="url(#serviceGradient)" rx="12"/>
            <text x="370" y="865" text-anchor="middle" fill="white" font-size="18" font-weight="bold">风控服务</text>
            <text x="370" y="885" text-anchor="middle" fill="white" font-size="13">Risk Control</text>
            <text x="370" y="905" text-anchor="middle" fill="white" font-size="11">反欺诈、限流</text>
            <text x="370" y="920" text-anchor="middle" fill="white" font-size="11">异常检测</text>
            
            <rect x="480" y="820" width="180" height="120" fill="url(#serviceGradient)" rx="12"/>
            <text x="570" y="865" text-anchor="middle" fill="white" font-size="18" font-weight="bold">消息队列</text>
            <text x="570" y="885" text-anchor="middle" fill="white" font-size="13">Message Queue</text>
            <text x="570" y="905" text-anchor="middle" fill="white" font-size="11">Kafka + RabbitMQ</text>
            <text x="570" y="920" text-anchor="middle" fill="white" font-size="11">异步处理</text>
            
            <rect x="680" y="820" width="180" height="120" fill="url(#serviceGradient)" rx="12"/>
            <text x="770" y="865" text-anchor="middle" fill="white" font-size="18" font-weight="bold">文件服务</text>
            <text x="770" y="885" text-anchor="middle" fill="white" font-size="13">File Storage</text>
            <text x="770" y="905" text-anchor="middle" fill="white" font-size="11">OSS + CDN</text>
            <text x="770" y="920" text-anchor="middle" fill="white" font-size="11">图片压缩</text>
            
            <rect x="880" y="820" width="180" height="120" fill="url(#serviceGradient)" rx="12"/>
            <text x="970" y="865" text-anchor="middle" fill="white" font-size="18" font-weight="bold">搜索服务</text>
            <text x="970" y="885" text-anchor="middle" fill="white" font-size="13">Search Engine</text>
            <text x="970" y="905" text-anchor="middle" fill="white" font-size="11">ES + 智能推荐</text>
            <text x="970" y="920" text-anchor="middle" fill="white" font-size="11">全文检索</text>
            
            <rect x="1080" y="820" width="180" height="120" fill="url(#serviceGradient)" rx="12"/>
            <text x="1170" y="865" text-anchor="middle" fill="white" font-size="18" font-weight="bold">通知服务</text>
            <text x="1170" y="885" text-anchor="middle" fill="white" font-size="13">Notification</text>
            <text x="1170" y="905" text-anchor="middle" fill="white" font-size="11">短信、邮件、推送</text>
            <text x="1170" y="920" text-anchor="middle" fill="white" font-size="11">模板管理</text>
            
            <rect x="1280" y="820" width="180" height="120" fill="url(#serviceGradient)" rx="12"/>
            <text x="1370" y="865" text-anchor="middle" fill="white" font-size="18" font-weight="bold">统计服务</text>
            <text x="1370" y="885" text-anchor="middle" fill="white" font-size="13">Analytics</text>
            <text x="1370" y="905" text-anchor="middle" fill="white" font-size="11">实时数据统计</text>
            <text x="1370" y="920" text-anchor="middle" fill="white" font-size="11">BI报表</text>

            <!-- 数据层 -->
            <rect x="80" y="1160" width="300" height="140" fill="url(#dataGradient)" rx="12"/>
            <text x="230" y="1210" text-anchor="middle" fill="white" font-size="22" font-weight="bold">PostgreSQL集群</text>
            <text x="230" y="1235" text-anchor="middle" fill="white" font-size="16">主数据库 (ACID)</text>
            <text x="230" y="1255" text-anchor="middle" fill="white" font-size="12">读写分离、分库分表</text>
            <text x="230" y="1275" text-anchor="middle" fill="white" font-size="12">定时备份、灾难恢复</text>
            
            <rect x="400" y="1160" width="300" height="140" fill="url(#dataGradient)" rx="12"/>
            <text x="550" y="1210" text-anchor="middle" fill="white" font-size="22" font-weight="bold">Redis集群</text>
            <text x="550" y="1235" text-anchor="middle" fill="white" font-size="16">内存缓存/会话</text>
            <text x="550" y="1255" text-anchor="middle" fill="white" font-size="12">热点数据缓存</text>
            <text x="550" y="1275" text-anchor="middle" fill="white" font-size="12">分布式锁、队列</text>
            
            <rect x="720" y="1160" width="300" height="140" fill="url(#dataGradient)" rx="12"/>
            <text x="870" y="1210" text-anchor="middle" fill="white" font-size="22" font-weight="bold">MongoDB</text>
            <text x="870" y="1235" text-anchor="middle" fill="white" font-size="16">文档数据库</text>
            <text x="870" y="1255" text-anchor="middle" fill="white" font-size="12">日志存储、用户行为</text>
            <text x="870" y="1275" text-anchor="middle" fill="white" font-size="12">复制集、分片</text>
            
            <rect x="1040" y="1160" width="300" height="140" fill="url(#dataGradient)" rx="12"/>
            <text x="1190" y="1210" text-anchor="middle" fill="white" font-size="22" font-weight="bold">Elasticsearch</text>
            <text x="1190" y="1235" text-anchor="middle" fill="white" font-size="16">搜索引擎</text>
            <text x="1190" y="1255" text-anchor="middle" fill="white" font-size="12">全文检索、日志分析</text>
            <text x="1190" y="1275" text-anchor="middle" fill="white" font-size="12">ELK栈、监控告警</text>

            <!-- 外部系统 -->
            <rect x="80" y="1480" width="240" height="100" fill="url(#externalGradient)" rx="12"/>
            <text x="200" y="1520" text-anchor="middle" fill="white" font-size="20" font-weight="bold">税务系统</text>
            <text x="200" y="1545" text-anchor="middle" fill="white" font-size="12">税率计算、申报</text>
            <text x="200" y="1560" text-anchor="middle" fill="white" font-size="12">电子发票</text>
            
            <rect x="340" y="1480" width="240" height="100" fill="url(#externalGradient)" rx="12"/>
            <text x="460" y="1520" text-anchor="middle" fill="white" font-size="20" font-weight="bold">政务系统</text>
            <text x="460" y="1545" text-anchor="middle" fill="white" font-size="12">营业执照验证</text>
            <text x="460" y="1560" text-anchor="middle" fill="white" font-size="12">资质审核</text>
            
            <rect x="600" y="1480" width="240" height="100" fill="url(#externalGradient)" rx="12"/>
            <text x="720" y="1520" text-anchor="middle" fill="white" font-size="20" font-weight="bold">银行系统</text>
            <text x="720" y="1545" text-anchor="middle" fill="white" font-size="12">资金清算、对账</text>
            <text x="720" y="1560" text-anchor="middle" fill="white" font-size="12">风控审核</text>
            
            <rect x="860" y="1480" width="240" height="100" fill="url(#externalGradient)" rx="12"/>
            <text x="980" y="1520" text-anchor="middle" fill="white" font-size="20" font-weight="bold">支付渠道</text>
            <text x="980" y="1545" text-anchor="middle" fill="white" font-size="12">微信、支付宝、银联</text>
            <text x="980" y="1560" text-anchor="middle" fill="white" font-size="12">跨境支付</text>
            
            <rect x="1120" y="1480" width="240" height="100" fill="url(#externalGradient)" rx="12"/>
            <text x="1240" y="1520" text-anchor="middle" fill="white" font-size="20" font-weight="bold">第三方物流</text>
            <text x="1240" y="1545" text-anchor="middle" fill="white" font-size="12">顺丰、通达、京东</text>
            <text x="1240" y="1560" text-anchor="middle" fill="white" font-size="12">智能调度、跟踪</text>

            <!-- 连接线 -->
            <!-- 客户端到网关 -->
            <line x1="300" y1="360" x2="360" y2="440" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
            <line x1="600" y1="360" x2="520" y2="440" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
            <line x1="900" y1="360" x2="820" y2="440" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
            <line x1="1200" y1="360" x2="1120" y2="440" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>

            <!-- 网关到服务层 -->
            <line x1="220" y1="560" x2="190" y2="660" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
            <line x1="520" y1="560" x2="430" y2="660" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
            <line x1="820" y1="560" x2="670" y2="660" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
            <line x1="1120" y1="560" x2="910" y2="660" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>

            <!-- 服务层到数据层 -->
            <line x1="230" y1="940" x2="230" y2="1160" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
            <line x1="550" y1="940" x2="550" y2="1160" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
            <line x1="870" y1="940" x2="870" y2="1160" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
            <line x1="1190" y1="940" x2="1190" y2="1160" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>

            <!-- 服务层到外部系统 -->
            <line x1="460" y1="940" x2="460" y2="1480" stroke="#9b59b6" stroke-width="3" stroke-dasharray="8,8"/>
            <line x1="1150" y1="800" x2="980" y2="1480" stroke="#9b59b6" stroke-width="3" stroke-dasharray="8,8"/>
            <line x1="1390" y1="800" x2="1240" y2="1480" stroke="#9b59b6" stroke-width="3" stroke-dasharray="8,8"/>

            <!-- 技术栈标注 -->
            <rect x="1600" y="120" width="540" height="320" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="12"/>
            <text x="1870" y="155" text-anchor="middle" fill="#2c3e50" font-size="24" font-weight="bold">技术栈详情 (Tech Stack)</text>
            
            <text x="1620" y="185" fill="#e74c3c" font-size="16" font-weight="bold">后端框架:</text>
            <text x="1620" y="205" fill="#495057" font-size="14">• Python 3.11 + FastAPI</text>
            <text x="1620" y="225" fill="#495057" font-size="14">• Pydantic 数据验证</text>
            <text x="1620" y="245" fill="#495057" font-size="14">• SQLAlchemy ORM</text>
            <text x="1620" y="265" fill="#495057" font-size="14">• AsyncIO 异步支持</text>
            
            <text x="1620" y="295" fill="#3498db" font-size="16" font-weight="bold">Web前端 (统一):</text>
            <text x="1620" y="315" fill="#495057" font-size="14">• React 18 + TypeScript</text>
            <text x="1620" y="335" fill="#495057" font-size="14">• Ant Design Pro 组件库</text>
            <text x="1620" y="355" fill="#495057" font-size="14">• Vite 构建工具</text>
            <text x="1620" y="375" fill="#495057" font-size="14">• PWA 支持</text>
            
            <text x="1620" y="405" fill="#9b59b6" font-size="16" font-weight="bold">移动端 (统一):</text>
            <text x="1620" y="425" fill="#495057" font-size="14">• Flutter 3.16 (Dart 3.2)</text>
            <text x="1620" y="445" fill="#495057" font-size="14">• 微信小程序原生开发</text>

            <!-- 非功能性需求标注 -->
            <rect x="1600" y="460" width="540" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="12"/>
            <text x="1870" y="490" text-anchor="middle" fill="#2c3e50" font-size="24" font-weight="bold">性能与部署 (Performance & Deployment)</text>
            
            <text x="1620" y="520" fill="#e67e22" font-size="16" font-weight="bold">性能指标:</text>
            <text x="1620" y="540" fill="#495057" font-size="14">• 并发用户: 10万+ (QPS: 5000+)</text>
            <text x="1620" y="560" fill="#495057" font-size="14">• API响应: &lt;200ms (P99: &lt;500ms)</text>
            <text x="1620" y="580" fill="#495057" font-size="14">• 系统可用性: 99.9% (SLA)</text>
            <text x="1620" y="600" fill="#495057" font-size="14">• 数据一致性: 强一致 (ACID)</text>
            <text x="1620" y="620" fill="#495057" font-size="14">• 数据备份: RPO&lt;1h, RTO&lt;4h</text>
            
            <text x="1620" y="650" fill="#27ae60" font-size="16" font-weight="bold">部署架构:</text>
            <text x="1620" y="670" fill="#495057" font-size="14">• Docker + Kubernetes 1.28+</text>
            <text x="1620" y="690" fill="#495057" font-size="14">• Helm Charts 版本管理</text>
            <text x="1620" y="710" fill="#495057" font-size="14">• CI/CD: GitLab + ArgoCD</text>
            <text x="1620" y="730" fill="#495057" font-size="14">• 蓝绿部署、金丝雀发布</text>
            <text x="1620" y="750" fill="#495057" font-size="14">• 多环境: Dev/Test/Staging/Prod</text>

            <!-- 监控与运维 -->
            <rect x="1600" y="780" width="540" height="200" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="12"/>
            <text x="1870" y="810" text-anchor="middle" fill="#2c3e50" font-size="24" font-weight="bold">监控与运维 (DevOps & Monitoring)</text>
            
            <text x="1620" y="840" fill="#8e44ad" font-size="16" font-weight="bold">监控系统:</text>
            <text x="1620" y="860" fill="#495057" font-size="14">• Prometheus + Grafana 指标监控</text>
            <text x="1620" y="880" fill="#495057" font-size="14">• ELK Stack 日志集中分析</text>
            <text x="1620" y="900" fill="#495057" font-size="14">• Jaeger 分布式链路跟踪</text>
            <text x="1620" y="920" fill="#495057" font-size="14">• SkyWalking APM 性能监控</text>
            <text x="1620" y="940" fill="#495057" font-size="14">• AlertManager 智能告警</text>
            <text x="1620" y="960" fill="#495057" font-size="14">• PagerDuty 事件响应</text>
            
            <!-- 图例说明 -->
            <g transform="translate(1600, 1000)">
                <rect x="0" y="0" width="540" height="260" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="12"/>
                <text x="270" y="30" text-anchor="middle" fill="#2c3e50" font-size="24" font-weight="bold">架构说明与图例</text>
                
                <text x="20" y="60" fill="#2c3e50" font-size="16" font-weight="bold">架构特点:</text>
                <text x="20" y="80" fill="#495057" font-size="14">• 微服务架构，服务独立部署、扩缩</text>
                <text x="20" y="100" fill="#495057" font-size="14">• 事件驱动，异步消息处理机制</text>
                <text x="20" y="120" fill="#495057" font-size="14">• CQRS + Event Sourcing 数据架构</text>
                <text x="20" y="140" fill="#495057" font-size="14">• 容器化部署，自动扩缩伸缩</text>
                <text x="20" y="160" fill="#495057" font-size="14">• 多租户架构，数据隔离</text>
                
                <text x="20" y="190" fill="#2c3e50" font-size="16" font-weight="bold">通信协议:</text>
                <line x1="20" y1="210" x2="60" y2="210" stroke="#34495e" stroke-width="3"/>
                <text x="70" y="217" fill="#495057" font-size="14">内部服务通信 (gRPC + HTTP/2)</text>
                
                <line x1="300" y1="210" x2="340" y2="210" stroke="#9b59b6" stroke-width="3" stroke-dasharray="6,6"/>
                <text x="350" y="217" fill="#495057" font-size="14">外部系统集成 (REST/SOAP/WebHook)</text>
                
                <text x="20" y="240" fill="#2c3e50" font-size="16" font-weight="bold">安全策略:</text>
                <text x="20" y="260" fill="#495057" font-size="14">• 零信任网络、最小权限原则</text>
            </g>
        </svg>
        </div>
    </div>
</body>
</html>